@import url('https://fonts.googleapis.com/css2?family=Nata+Sans:wght@100..900&display=swap');
:root{
  --icon-active-hover: #aa3540;
  --text-white-primary: #c4c2c5;
  --background-color: #232428;  
}
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}


body{
  font-family: "nata-sans",'montserrat', sans-serif;
  background-color: var(--background-color); /* background color */
  color: #c4c2c5;  
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
  font-weight: 100;
}

.side-bar-container {
  color: #c4c2c5;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 70px;
  align-items: center;
  padding: 20px 0;
}

.side-bar-container .logo {
  color: var(--icon-active-hover);
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  justify-self: center;
  gap: 40px;
  flex-grow: 1;
}

.tool-category {
  color: var(--text-white-primary);
  display: flex;
  padding: 12px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  font-size: 40px;
  font-weight: 300;
  transition: var(--transition-smooth);
}
.tool-category:focus, .tool-category:hover {
  outline: none;
  font-weight: 700;
  border-left: 2px solid #aa3540;
}
.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}


.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  gap: 20px;
}

.tools-cards-section{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  max-width: 300px;
  margin: 0;
  gap: 20px;
}
.tools-cards-content{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  margin: 0;
  gap: 20px;
}

.tool-card{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  max-height: 90px;
  margin: 0;
  border-left: 4px solid #aa3540;
  transition: all 0.3s ease-in-out;
}
.tool-card:hover{
  transform: scale(1.02);
  transform: translateY(-5px);
  border-left: 4px solid #622930;
}

.box-title {
  display: flex;
  align-items: center;
  gap: 1px;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.box-title i {
  color: var(--accent-primary);
  font-size: var(--font-size-2xl);
  margin-left: auto;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
}

h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
}

/* AutoSpace Section */
.autospace-section {
  padding: var(--spacing-3xl);
  margin-bottom: var(--spacing-xl);
}

.default-view {
  color: var(--text-tertiary);
  font-size: var(--font-size-lg);
  text-align: center;
  padding: var(--spacing-5xl) var(--spacing-xl);
  font-style: italic;
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: 1px;
  padding: var(--spacing-3xl);
}

.tool-name {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.tool-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  font-weight: var(--font-weight-normal);
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: 1px;
  align-items: flex-start;
}

/* Button Styles */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--button-padding-y) var(--button-padding-x);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  background-color: var(--surface-secondary);
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.run-button:hover, .download-sample-button:hover {
  background-color: var(--surface-hover);
  color: var(--accent-primary);
  transform: translateY(-1px);
}

.run-button:active, .download-sample-button:active {
  transform: translateY(0);
  background-color: var(--surface-active);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--button-padding-y) var(--button-padding-x);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  background-color: var(--surface-secondary);
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.file-label:hover {
  background-color: var(--surface-hover);
  color: var(--accent-primary);
  transform: translateY(-1px);
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 18px;
  height: 18px;
  background-color: var(--surface-secondary);
  cursor: pointer;
  position: relative;
  transition: var(--transition-base);
  margin-right: var(--spacing-sm);
}

input[type="checkbox"]:checked {
  background-color: var(--accent-primary);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-inverse);
  font-weight: bold;
  font-size: 12px;
}

label[for="mode"] {
  color: var(--text-secondary);
  font-weight: var(--font-weight-normal);
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: var(--font-size-base);
}

label[for="mode"]:hover {
  color: var(--text-primary);
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: 1px;
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: var(--spacing-3xl);
}

.performance-box, .users-box {
  padding: var(--spacing-xl);
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
}

.metric-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background-color: var(--surface-secondary);
  transition: var(--transition-smooth);
  cursor: pointer;
  aspect-ratio: 1;
  min-height: 80px;
}

.metric-card:hover {
  background-color: var(--surface-hover);
  transform: translateY(-1px);
}

.metric-content {
  display: flex;
  flex-direction: column;
  font-weight: var(--font-weight-bold);
}

.metric-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.metric-value {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

.metric-icon {
  margin-left: auto;
}

.metric-icon i {
  color: var(--accent-primary);
  font-size: var(--font-size-2xl);
}

.uptime-card {
  background-color: var(--accent-primary);
}

.uptime-card .metric-label,
.uptime-card .metric-value {
  color: var(--text-inverse);
}

.uptime-card .metric-icon i {
  color: var(--text-inverse);
}

/* User Count Badge */
.user-count {
  background-color: var(--accent-primary);
  color: var(--text-inverse);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-left: auto;
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: var(--spacing-5xl) var(--spacing-xl);
  color: var(--text-tertiary);
  font-size: var(--font-size-base);
}

.no-users i {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-lg);
  color: var(--accent-primary);
}

.user-card {
  background-color: var(--surface-secondary);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: var(--transition-smooth);
  cursor: pointer;
}

.user-card:hover {
  background-color: var(--surface-hover);
  transform: translateY(-1px);
}

.user-card.current-user {
  background-color: var(--accent-primary-subtle);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 1px;
  margin-bottom: var(--spacing-sm);
}

.user-header i {
  color: var(--accent-primary);
  font-size: var(--font-size-lg);
  margin-left: auto;
}

.username {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  flex-grow: 1;
  font-size: var(--font-size-base);
}

.session-badge {
  background-color: var(--accent-primary-muted);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
}

.last-activity {
  color: var(--text-tertiary);
  font-style: italic;
}

.activity-type {
  color: var(--text-secondary);
  background-color: var(--surface-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

/* Session Cards Styles */
.session-card {
  background-color: var(--surface-secondary);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: var(--transition-smooth);
  position: relative;
  cursor: pointer;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: var(--border-width-accent);
  height: 100%;
  background-color: var(--accent-primary);
  transition: var(--transition-smooth);
}

.session-card:hover {
  background-color: var(--surface-hover);
  transform: translateY(-1px);
}

.session-card:hover::before {
  width: 6px;
  background-color: var(--accent-primary-hover);
}

.session-card .title {
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

.session-card .time {
  color: var(--text-tertiary);
  font-size: var(--font-size-xs);
  background-color: var(--surface-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  display: inline-block;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

.progress-counter {
  color: var(--accent-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  background-color: var(--accent-primary-subtle);
  padding: var(--spacing-xs) var(--spacing-sm);
  display: inline-block;
  margin-bottom: var(--spacing-sm);
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background-color: var(--surface-secondary);
  color: var(--text-primary);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-right: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.refresh-button:hover {
  background-color: var(--accent-primary);
  color: var(--text-inverse);
  transform: translateY(-1px);
}

.kill-button:hover {
  background-color: var(--status-error);
  color: var(--text-inverse);
  transform: translateY(-1px);
}

.download-button:hover {
  background-color: var(--status-success);
  color: var(--text-inverse);
  transform: translateY(-1px);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: var(--surface-secondary);
}

::-webkit-scrollbar-thumb {
  background-color: var(--accent-primary);
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--accent-primary-hover);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 60px;
  }

  .main-content {
    padding: var(--spacing-lg);
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }

  .middle-section, .sessions-section {
    padding: var(--spacing-xl);
  }

  .system-performance-section, .active-users-section {
    padding: var(--spacing-xl);
  }
}