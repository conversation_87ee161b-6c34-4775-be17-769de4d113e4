@import url('https://fonts.googleapis.com/css2?family=Nata+Sans:wght@100..900&display=swap');

:root {
  /* Typography Variables */
  --font-family-primary: "nata-sans", 'montserrat', sans-serif;
  --font-size-title-large: 3rem;
  --font-size-title-medium: 1.5rem;
  --font-size-title-small: 1.2rem;
  --font-size-regular: 1rem;
  --font-size-small: 0.875rem;
  --font-size-icon-large: 40px;
  --font-size-icon-medium: 24px;
  --font-size-icon-small: 20px;

  /* Font Weights */
  --font-weight-light: 100;
  --font-weight-regular: 300;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Color Variables - Named by Function */
  --color-primary-accent: #aa3540;
  --color-primary-accent-dark: #622930;
  --color-text-primary: #c4c2c5;
  --color-text-secondary: #9a9a9a;
  --color-background-main: #232428;
  --color-background-card: #242425;
  --color-background-overlay: rgba(36, 36, 37, 0.8);

  /* Spacing Variables */
  --spacing-minimal: 1px;
  --spacing-small: 8px;
  --spacing-medium: 12px;
  --spacing-large: 20px;
  --spacing-extra-large: 25px;
  --spacing-huge: 40px;

  /* Layout Variables */
  --sidebar-width: 70px;
  --card-max-height: 90px;
  --tools-section-max-width: 300px;
  --border-radius-small: 4px;
  --border-radius-medium: 8px;
  --border-radius-large: 15px;

  /* Shadow Variables */
  --shadow-subtle: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-strong: 0 8px 30px rgba(0, 0, 0, 0.25);

  /* Transition Variables */
  --transition-fast: 0.2s ease-in-out;
  --transition-smooth: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;

  /* Interactive States */
  --hover-scale-subtle: 1.02;
  --hover-translate-up: -5px;
  --focus-outline-width: 2px;

  /* Glass Effect Variables */
  --glass-blur-light: blur(10px);
  --glass-blur-medium: blur(15px);
  --glass-bg-primary: rgba(35, 36, 40, 0.7);
  --glass-bg-secondary: rgba(36, 36, 37, 0.6);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-primary);
  background-color: var(--color-background-main);
  color: var(--color-text-primary);
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  overflow-x: hidden;
  font-weight: var(--font-weight-light);
  font-size: var(--font-size-regular);
}

.side-bar-container {
  color: var(--color-text-primary);
  background-color: var(--color-background-main);
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: var(--sidebar-width);
  align-items: center;
  padding: var(--spacing-large) 0;
}

.side-bar-container .logo {
  color: var(--color-primary-accent);
  font-size: var(--font-size-title-large);
  margin-bottom: var(--spacing-large);
  font-weight: var(--font-weight-bold);
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  justify-self: center;
  gap: var(--spacing-huge);
  flex-grow: 1;
}

.tool-category {
  color: var(--color-text-primary);
  display: flex;
  padding: var(--spacing-medium);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  font-size: var(--font-size-icon-large);
  font-weight: var(--font-weight-regular);
  transition: var(--transition-smooth);
}

.tool-category:focus,
.tool-category:hover {
  outline: none;
  font-weight: var(--font-weight-bold);
  border-left: var(--focus-outline-width) solid var(--color-primary-accent);
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: var(--spacing-large);
  gap: var(--spacing-minimal);
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: var(--spacing-extra-large);
  overflow-y: auto;
  gap: var(--spacing-minimal);
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-small);
  overflow-y: auto;
  max-width: var(--tools-section-max-width);
  margin: 0;
  gap: var(--spacing-minimal);
}

.tools-cards-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-small);
  overflow-y: auto;
  margin: 0;
  gap: var(--spacing-minimal);
}

.tool-card {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-small);
  overflow-y: auto;
  max-height: var(--card-max-height);
  margin: 0;
  transition: var(--transition-smooth);
  cursor: pointer;
}

.tool-card:hover {
  transform: scale(var(--hover-scale-subtle)) translateY(var(--hover-translate-up));
}

.box-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-small);
  font-size: var(--font-size-title-medium);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-large);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-extra-large);
}

.system-performance-section {
  padding: var(--spacing-extra-large);
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-minimal);
}

.metric-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-medium);
  box-shadow: var(--shadow-subtle);
  aspect-ratio: 1;
  position: relative;
  transition: var(--transition-smooth);
}

.metric-card:hover {
  transform: scale(var(--hover-scale-subtle));
  box-shadow: var(--shadow-medium);
}

.metric-content {
  font-weight: var(--font-weight-bold);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
  flex-grow: 1;
}

.metric-icon {
  position: absolute;
  top: var(--spacing-medium);
  right: var(--spacing-medium);
  font-size: var(--font-size-icon-medium);
  color: var(--color-text-secondary);
}

.metric-label {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: var(--font-size-title-small);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

/* Section Styling */
.section {
  gap: var(--spacing-minimal);
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 2;
  gap: var(--spacing-minimal);
}

.autospace-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
  padding: var(--spacing-extra-large);
}

.right-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
  max-width: var(--tools-section-max-width);
}

/* Active Users Section */
.active-users-section {
  padding: var(--spacing-extra-large);
}

.users-box {
  gap: var(--spacing-minimal);
}

.users-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
}

.user-card {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
  padding: var(--spacing-medium);
  border-radius: var(--border-radius-medium);
  transition: var(--transition-smooth);
}

.user-card:hover {
  transform: scale(var(--hover-scale-subtle));
}

.user-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-small);
}

.username {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.session-badge {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  margin-left: auto;
}

.user-info {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
}

.current-user {
  color: var(--color-primary-accent);
}

.no-users {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-small);
  padding: var(--spacing-large);
  color: var(--color-text-secondary);
  text-align: center;
}

/* Button Styling */
.run-button,
.refresh-button,
.kill-button,
.download-button {
  padding: var(--spacing-medium) var(--spacing-large);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: var(--transition-smooth);
  font-size: var(--font-size-small);
}

.run-button {
  background-color: var(--color-primary-accent);
  color: var(--color-text-primary);
}

.run-button:hover {
  background-color: var(--color-primary-accent-dark);
  transform: scale(var(--hover-scale-subtle));
}

/* Session Cards */
.session-card {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
  padding: var(--spacing-medium);
  transition: var(--transition-smooth);
}

.session-card:hover {
  transform: scale(var(--hover-scale-subtle));
}

.session-card .title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.time {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
}

.progress-counter {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-accent);
}

/* Form Elements */
.file-label {
  padding: var(--spacing-medium) var(--spacing-large);
  cursor: pointer;
  transition: var(--transition-smooth);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
}

input[type="file"] {
  display: none;
}

input[type="checkbox"] {
  margin-right: var(--spacing-small);
}

label {
  font-size: var(--font-size-small);
  color: var(--color-text-primary);
}

/* Default and Selection Views */
.default-view {
  padding: var(--spacing-extra-large);
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
}

.tool-name {
  font-size: var(--font-size-title-medium);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.tool-description {
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.tool-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-minimal);
  align-items: center;
}

/* Category Title */
.category-title {
  font-size: var(--font-size-title-medium);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
}